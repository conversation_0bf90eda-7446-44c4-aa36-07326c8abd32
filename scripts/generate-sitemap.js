/**
 * Advanced Sitemap Generator for Yu-Gi-Oh! Card Maker
 * Generates XML sitemap with multi-language support and proper formatting
 */

const fs = require('fs')
const path = require('path')

// Configuration
const config = {
  hostname: 'https://yugiohcardmaker.org',
  languages: ['zh', 'en', 'jp'],
  defaultLanguage: 'zh',
  outputPath: path.join(__dirname, '../static/sitemap.xml')
}

// Page definitions with metadata
const pages = [
  {
    path: '/',
    priority: 1.0,
    changefreq: 'weekly',
    multilang: true,
    description: 'Yu-Gi-Oh! Card Maker - Main page'
  },
  {
    path: '/privacy',
    priority: 0.3,
    changefreq: 'monthly',
    multilang: true,
    description: 'Privacy Policy'
  },
  {
    path: '/terms',
    priority: 0.3,
    changefreq: 'monthly',
    multilang: true,
    description: 'Terms of Service'
  }
]

/**
 * Generate XML sitemap with proper formatting
 */
function generateSitemap() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n'
  xml += '        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n'

  pages.forEach(page => {
    // Main URL (default language)
    xml += '  <url>\n'
    xml += `    <loc>${config.hostname}${page.path}</loc>\n`
    xml += `    <lastmod>${currentDate}</lastmod>\n`
    xml += `    <changefreq>${page.changefreq}</changefreq>\n`
    xml += `    <priority>${page.priority}</priority>\n`
    
    // Add alternate language links if multilingual
    if (page.multilang) {
      config.languages.forEach(lang => {
        const langUrl = lang === config.defaultLanguage 
          ? `${config.hostname}${page.path}`
          : `${config.hostname}${page.path}${page.path === '/' ? '' : '/'}?lang=${lang}`
        
        xml += `    <xhtml:link rel="alternate" hreflang="${lang}" href="${langUrl}" />\n`
      })
      
      // Add x-default for international targeting
      xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${config.hostname}${page.path}" />\n`
    }
    
    xml += '  </url>\n'
  })

  xml += '</urlset>\n'
  
  return xml
}

/**
 * Generate sitemap index for multiple sitemaps (future expansion)
 */
function generateSitemapIndex() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
  xml += '  <sitemap>\n'
  xml += `    <loc>${config.hostname}/sitemap.xml</loc>\n`
  xml += `    <lastmod>${currentDate}</lastmod>\n`
  xml += '  </sitemap>\n'
  xml += '</sitemapindex>\n'
  
  return xml
}

/**
 * Write sitemap to file
 */
function writeSitemap() {
  try {
    const sitemapXml = generateSitemap()
    
    // Ensure directory exists
    const dir = path.dirname(config.outputPath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    
    // Write main sitemap
    fs.writeFileSync(config.outputPath, sitemapXml, 'utf8')
    console.log(`✅ Sitemap generated successfully: ${config.outputPath}`)
    
    // Generate sitemap index (for future use)
    const indexPath = path.join(__dirname, '../static/sitemap-index.xml')
    const indexXml = generateSitemapIndex()
    fs.writeFileSync(indexPath, indexXml, 'utf8')
    console.log(`✅ Sitemap index generated: ${indexPath}`)
    
    // Log sitemap contents
    console.log('\n📋 Sitemap Contents:')
    pages.forEach(page => {
      console.log(`   ${config.hostname}${page.path} (Priority: ${page.priority}, Changefreq: ${page.changefreq})`)
      if (page.multilang) {
        config.languages.forEach(lang => {
          const langUrl = lang === config.defaultLanguage 
            ? `${config.hostname}${page.path}`
            : `${config.hostname}${page.path}${page.path === '/' ? '' : '/'}?lang=${lang}`
          console.log(`     └─ ${lang}: ${langUrl}`)
        })
      }
    })
    
    console.log(`\n📊 Total URLs: ${pages.length}`)
    console.log(`🌐 Languages: ${config.languages.join(', ')}`)
    console.log(`🔗 Hostname: ${config.hostname}`)
    
  } catch (error) {
    console.error('❌ Error generating sitemap:', error)
    process.exit(1)
  }
}

// Generate sitemap if run directly
if (require.main === module) {
  writeSitemap()
}

module.exports = {
  generateSitemap,
  generateSitemapIndex,
  writeSitemap,
  config,
  pages
}
