# 遊戲王卡片製造機 | Yu-Gi-Oh! Card Maker

![GitHub last commit](https://img.shields.io/github/last-commit/linziyou0601/yugioh-card-maker?style=for-the-badge) ![](https://img.shields.io/badge/author-linziyou0601-red.svg?style=for-the-badge) ![](https://img.shields.io/badge/language-vue-blue.svg?style=for-the-badge)

## 📖 項目簡介

遊戲王卡片製造機是一個基於 Nuxt.js 的靜態網站應用，允許用戶創建個人化的遊戲王卡片。該項目使用 HTML5 Canvas 技術進行卡片渲染，支持多種語言和卡片類型，提供豐富的自定義選項。

### 🌟 主要特性

- **多語言支持**：支持正體中文、日文、英文界面和卡片語言
- **豐富的卡片類型**：支持怪獸卡、魔法卡、陷阱卡及其各種子類型
- **靈擺卡支持**：完整的靈擺怪獸卡制作功能
- **連結怪獸**：支持連結怪獸的連結標記設置
- **自動數據填充**：支持通過卡片密碼自動填充2019年9月前的卡片數據
- **實時預覽**：所見即所得的卡片編輯體驗
- **3D 視覺效果**：卡片預覽區域具有 3D 懸停效果
- **高質量輸出**：支持高分辨率卡片圖片下載

## 🌐 在線演示

**官方演示地址**：https://yugioh-card.linziyou.info/

## 🛠 技術架構

### 核心技術棧

- **前端框架**：Nuxt.js 2.15.7 (Vue.js 2.x)
- **UI 組件庫**：Bootstrap Vue 2.21.2
- **樣式框架**：Bootstrap 4.6.0
- **圖標庫**：Font Awesome 5.15.4
- **字體加載**：nuxt-font-loader
- **構建工具**：Webpack (Nuxt.js 內置)
- **代碼規範**：ESLint + Prettier

### 渲染技術

- **Canvas API**：用於卡片圖像渲染和文字繪製
- **圖像處理**：支持用戶上傳圖片的動態裁剪和縮放
- **字體渲染**：多語言字體支持，包括中文、日文、英文專用字體
- **實時計算**：動態文字換行和布局計算

## 📁 項目結構

```
yugioh-card-maker/
├── components/                 # Vue 組件
│   └── LoadingDialog.vue      # 加載對話框組件
├── pages/                     # 頁面文件
│   └── index.vue              # 主頁面（卡片製造器）
├── plugins/                   # Nuxt.js 插件
│   └── font-awesome.js        # Font Awesome 配置
├── static/                    # 靜態資源
│   ├── fonts/                 # 字體文件
│   │   ├── MatrixBoldSmallCaps.ttf
│   │   ├── cardkey.ttf
│   │   ├── cn.ttf, jp.ttf, zh.ttf
│   │   └── font-face.css
│   ├── images/                # 圖片資源
│   │   ├── attr/              # 屬性圖標
│   │   ├── card/              # 卡片模板
│   │   └── pic/               # 其他圖片素材
│   ├── ygo/                   # 遊戲王數據
│   │   ├── card_data.json     # 卡片數據庫
│   │   └── pics/              # 卡片圖片
│   ├── lang.ui.json           # 界面語言配置
│   └── lang.card_meta.json    # 卡片語言配置
├── store/                     # Vuex 狀態管理
│   └── index.js               # 主要狀態管理
├── nuxt.config.js             # Nuxt.js 配置文件
├── package.json               # 項目依賴配置
└── README.md                  # 項目文檔
```

## 🚀 安裝和運行

### 環境要求

- Node.js >= 14.x
- npm >= 6.x 或 yarn >= 1.x

### 安裝步驟

1. **克隆項目**
```bash
git clone https://github.com/linziyou0601/yugioh-card-maker.git
cd yugioh-card-maker
```

2. **安裝依賴**
```bash
npm install
# 或
yarn install
```

3. **開發模式運行**
```bash
npm run dev
# 或
yarn dev
```

4. **訪問應用**
打開瀏覽器訪問 `http://localhost:3000`

### 構建和部署

```bash
# 構建生產版本
npm run build
npm run start

# 生成靜態文件
npm run generate

# GitHub Pages 部署
npm run generate:gh-pages
```

## 📚 使用指南

### 基本操作

1. **選擇語言**：在界面頂部選擇界面語言和卡片語言
2. **設置基本屬性**：
   - 防偽貼：開啟/關閉防偽標記
   - 稀有度：N（普通）、R（稀有）、UR（超稀有）
   - 標題顏色：自定義卡片名稱顏色

3. **填入卡片信息**：
   - 卡片密碼：可自動填充已有卡片數據
   - 卡片名稱：自定義卡片名稱
   - 上傳圖片：支持拖拽上傳

### 卡片類型設置

#### 怪獸卡
- **卡種**：選擇怪獸卡
- **卡面**：通常、效果、融合、儀式、同步、超量、連結、衍生物等
- **效果**：卡通、靈魂、聯合、二重、翻轉、調整等
- **屬性**：光、暗、地、水、火、風、神
- **種族**：龍族、戰士族、魔法師族等（支持自定義）
- **等級/階級**：1-12級
- **攻擊力/守備力**：支持數字和特殊符號（如∞）

#### 靈擺怪獸
- **靈擺效果**：開啟靈擺功能
- **靈擺刻度**：藍色和紅色刻度值（0-12）
- **靈擺效果文本**：靈擺效果描述

#### 連結怪獸
- **連結標記**：8個方向的連結箭頭設置
- **連結值**：自動計算選中的連結標記數量

#### 魔法/陷阱卡
- **子類型**：通常、永續、場地、裝備、速攻、儀式、反擊等

### 高級功能

1. **自動數據填充**：
   - 輸入8位卡片密碼
   - 系統自動填充2019年9月前的官方卡片數據

2. **文字大小調整**：
   - 卡片說明文字大小可調整
   - 靈擺效果文字大小可調整

3. **實時預覽**：
   - 修改任何設置都會實時更新卡片預覽
   - 支持3D懸停效果

4. **下載功能**：
   - 點擊下載按鈕保存高質量卡片圖片
   - 支持JPG格式輸出

## 🔧 開發指南

### 核心組件說明

#### 主頁面 (pages/index.vue)
- **數據管理**：包含所有卡片屬性的響應式數據
- **Canvas渲染**：核心的卡片繪製邏輯
- **用戶交互**：表單控件和實時預覽

#### 關鍵方法

```javascript
// 卡片繪製主流程
drawCard() {
  // 準備圖片資源
  // 載入所需圖片
  // 執行繪製流程
}

// Canvas繪製核心
drawCardProcess() {
  // 繪製底圖和卡片圖片
  // 繪製卡片標題
  // 繪製卡片信息文字
  // 繪製特殊效果（防偽貼、連結標記等）
}

// 文字自動換行
wrapText(ctx, text, x, y, maxWidth, lineHeight) {
  // 計算文字寬度
  // 自動換行處理
  // 繪製多行文字
}
```

### 多語言系統

#### 界面語言 (static/lang.ui.json)
```json
{
  "zh": {
    "name": "正體中文 (T.Chinese)",
    "card_name": "卡片名稱",
    "monster_card": "怪獸",
    // ... 更多界面文字
  }
}
```

#### 卡片語言 (static/lang.card_meta.json)
```json
{
  "zh": {
    "name": "正體中文",
    "_templateLang": "zh",
    "_fontName": ["zh", "zh", "zh", "zh", "cn", "zh"],
    "_offset": {
      "tS": 0, "sS": 0,
      "tX": 0, "tY": 0,
      // ... 更多偏移設置
    },
    "Race": {
      "dragon": "龍族",
      // ... 更多種族翻譯
    }
  }
}
```

### 添加新語言

1. **添加界面語言**：
   - 在 `static/lang.ui.json` 中添加新語言對象
   - 翻譯所有界面文字

2. **添加卡片語言**：
   - 在 `static/lang.card_meta.json` 中添加新語言對象
   - 設置字體和偏移參數
   - 準備對應的卡片模板圖片

3. **添加字體文件**：
   - 將字體文件放入 `static/fonts/` 目錄
   - 在 `font-face.css` 中定義字體

### 自定義卡片模板

1. **準備模板圖片**：
   - 尺寸：1000x1450 像素
   - 格式：PNG（支持透明）
   - 放置路徑：`static/images/card/{language}/{type}.png`

2. **配置模板映射**：
   - 在組件中的 `cardTemplateText` 計算屬性中添加邏輯
   - 確保文件名與卡片類型匹配

## 🤝 貢獻指南

### 開發流程

1. Fork 本項目
2. 創建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

### 代碼規範

- 使用 ESLint 和 Prettier 進行代碼格式化
- 遵循 Vue.js 官方風格指南
- 組件名使用 PascalCase
- 方法名使用 camelCase

### 測試

```bash
# 運行 ESLint 檢查
npm run lint

# 自動修復 ESLint 問題
npm run lint -- --fix
```

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 👨‍💻 作者

**Linziyou** - [GitHub](https://github.com/linziyou0601)

## 🙏 致謝

- 感謝所有為遊戲王卡片製造機項目做出貢獻的開發者
- 感謝 Konami 公司創造了精彩的遊戲王卡牌遊戲
- 感謝開源社區提供的優秀工具和庫

## 📞 支持

如果您在使用過程中遇到問題，請：

1. 查看本文檔的常見問題部分
2. 在 GitHub Issues 中搜索相關問題
3. 創建新的 Issue 描述您的問題

---

**享受創造屬於您自己的遊戲王卡片吧！** 🎴✨
