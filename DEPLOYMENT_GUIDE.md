# 部署指南 | Deployment Guide

## 📋 目錄

- [部署概述](#部署概述)
- [GitHub Pages 部署](#github-pages-部署)
- [Netlify 部署](#netlify-部署)
- [Vercel 部署](#vercel-部署)
- [自定義服務器部署](#自定義服務器部署)
- [Docker 部署](#docker-部署)
- [環境配置](#環境配置)
- [性能優化](#性能優化)
- [故障排除](#故障排除)

## 🌐 部署概述

遊戲王卡片製造機是一個基於 Nuxt.js 的靜態網站應用，支持多種部署方式：

- **靜態生成 (SSG)**：推薦用於生產環境
- **單頁應用 (SPA)**：適合 CDN 部署
- **服務端渲染 (SSR)**：需要 Node.js 服務器

### 項目特點

- ✅ 純前端應用，無需後端服務器
- ✅ 支持靜態文件部署
- ✅ 響應式設計，支持移動端
- ✅ 離線可用（PWA 特性可選）

## 🚀 GitHub Pages 部署

### 自動部署（推薦）

1. **Fork 項目到您的 GitHub 賬戶**

2. **啟用 GitHub Actions**
   - 進入項目的 Settings > Pages
   - Source 選擇 "GitHub Actions"

3. **創建部署工作流**
   
   創建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build
      run: npm run generate:gh-pages
      
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

### 手動部署

1. **本地構建**
```bash
# 安裝依賴
npm install

# 生成靜態文件
npm run generate:gh-pages

# 部署到 gh-pages 分支
npm install -g gh-pages
gh-pages -d dist
```

2. **配置 GitHub Pages**
   - 進入 Settings > Pages
   - Source 選擇 "Deploy from a branch"
   - Branch 選擇 "gh-pages"

### 自定義域名

1. **添加 CNAME 文件**
   
   在 `static/` 目錄下創建 `CNAME` 文件：
```
your-domain.com
```

2. **配置 DNS**
   - 添加 CNAME 記錄指向 `username.github.io`
   - 或添加 A 記錄指向 GitHub Pages IP

## 🌟 Netlify 部署

### 從 Git 部署

1. **連接 GitHub 倉庫**
   - 登錄 Netlify
   - 點擊 "New site from Git"
   - 選擇 GitHub 並授權

2. **配置構建設置**
   - Build command: `npm run generate`
   - Publish directory: `dist`
   - Node version: `16`

3. **環境變量**（可選）
```
NODE_VERSION=16
DEPLOY_ENV=NETLIFY
```

### 使用 netlify.toml 配置

創建 `netlify.toml`：

```toml
[build]
  command = "npm run generate"
  publish = "dist"

[build.environment]
  NODE_VERSION = "16"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/fonts/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## ⚡ Vercel 部署

### 從 Git 部署

1. **導入項目**
   - 登錄 Vercel
   - 點擊 "New Project"
   - 導入 GitHub 倉庫

2. **配置設置**
   - Framework Preset: `Nuxt.js`
   - Build Command: `npm run generate`
   - Output Directory: `dist`

### 使用 vercel.json 配置

創建 `vercel.json`：

```json
{
  "version": 2,
  "builds": [
    {
      "src": "nuxt.config.js",
      "use": "@nuxtjs/vercel-builder"
    }
  ],
  "routes": [
    {
      "src": "/fonts/(.*)",
      "headers": {
        "Cache-Control": "public, max-age=31536000, immutable"
      }
    },
    {
      "src": "/images/(.*)",
      "headers": {
        "Cache-Control": "public, max-age=31536000, immutable"
      }
    }
  ]
}
```

## 🖥 自定義服務器部署

### Nginx 配置

1. **構建靜態文件**
```bash
npm run generate
```

2. **Nginx 配置文件**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/yugioh-card-maker/dist;
    index index.html;

    # 啟用 gzip 壓縮
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 靜態資源緩存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 字體文件 CORS
    location ~* \.(woff|woff2|ttf|eot)$ {
        add_header Access-Control-Allow-Origin *;
    }

    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 安全頭
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

### Apache 配置

創建 `.htaccess` 文件：

```apache
RewriteEngine On

# 啟用 gzip 壓縮
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 靜態資源緩存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# SPA 路由支持
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# 字體文件 CORS
<FilesMatch "\.(woff|woff2|ttf|eot)$">
    Header set Access-Control-Allow-Origin "*"
</FilesMatch>
```

## 🐳 Docker 部署

### Dockerfile

```dockerfile
# 構建階段
FROM node:16-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run generate

# 生產階段
FROM nginx:alpine

# 複製構建文件
COPY --from=builder /app/dist /usr/share/nginx/html

# 複製 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  yugioh-card-maker:
    build: .
    ports:
      - "80:80"
    restart: unless-stopped
    
  # 可選：添加 SSL 終止
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
      - ./nginx-ssl.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - yugioh-card-maker
```

### 部署命令

```bash
# 構建並運行
docker-compose up -d

# 查看日誌
docker-compose logs -f

# 更新部署
docker-compose pull
docker-compose up -d
```

## ⚙️ 環境配置

### 環境變量

```bash
# .env 文件
NODE_ENV=production
DEPLOY_ENV=GH_PAGES
BASE_URL=https://your-domain.com
```

### Nuxt.js 配置

```javascript
// nuxt.config.js
export default {
  // 根據部署環境調整
  router: {
    base: process.env.DEPLOY_ENV === 'GH_PAGES' ? '/yugioh-card-maker/' : '/'
  },
  
  // 生產環境優化
  build: {
    extractCSS: true,
    optimization: {
      splitChunks: {
        chunks: 'all'
      }
    }
  }
}
```

## 🚀 性能優化

### 構建優化

1. **啟用壓縮**
```javascript
// nuxt.config.js
export default {
  build: {
    // 啟用 gzip 壓縮
    compress: true,
    
    // 分析包大小
    analyze: process.env.ANALYZE === 'true'
  }
}
```

2. **圖片優化**
```bash
# 安裝圖片優化工具
npm install --save-dev @nuxtjs/imagemin

# 配置
modules: [
  '@nuxtjs/imagemin'
]
```

### CDN 配置

```javascript
// nuxt.config.js
export default {
  build: {
    publicPath: process.env.NODE_ENV === 'production' 
      ? 'https://cdn.your-domain.com/_nuxt/' 
      : '/_nuxt/'
  }
}
```

## 🔧 故障排除

### 常見問題

1. **字體載入失敗**
   - 檢查字體文件路徑
   - 確認 CORS 設置
   - 驗證字體格式支持

2. **圖片顯示異常**
   - 檢查圖片路徑
   - 確認圖片格式
   - 驗證文件大小限制

3. **Canvas 渲染問題**
   - 檢查瀏覽器兼容性
   - 確認 Canvas 尺寸設置
   - 驗證圖片載入狀態

### 調試工具

```javascript
// 開發模式調試
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', {
    cardType: this.cardType,
    templatePath: this.cardTemplateText,
    imageLoaded: this.imgs.template.complete
  })
}
```

### 性能監控

```javascript
// 添加性能監控
const startTime = performance.now()
this.drawCardProcess()
const endTime = performance.now()

if (endTime - startTime > 1000) {
  console.warn('Slow rendering detected:', endTime - startTime, 'ms')
}
```

---

這份部署指南涵蓋了項目的各種部署方式和配置選項。根據您的需求選擇合適的部署方案，並按照相應的步驟進行配置。
